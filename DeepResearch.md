# **Google Veo3: A Precise Prompt Formula for Advanced Video Generation**

## **Introduction**

Google's Veo3 represents a significant advancement in generative artificial intelligence, offering sophisticated capabilities for creating video content from textual descriptions or existing images.1 A key attribute of Veo3, particularly the veo-3.0-generate-preview model, is its capacity for native audio generation, including synchronized dialogue, sound effects, and music, alongside high-fidelity visual output.1 This report aims to meticulously define and document the underlying **prompt formula** for Veo3. This formula encompasses the specific structure, components, syntax, keywords, operators, and their interrelations that users must understand to effectively guide the model.

Understanding this precise prompt formula is paramount for users seeking to transcend trial-and-error experimentation. It empowers them to exercise fine-grained control over the generation process, leading to more predictable and targeted outcomes. A notable characteristic of the veo-3.0-generate-preview model, accessible via Vertex AI, is the mandatory engagement of a prompt rewriter, termed enhancePrompt.4 This feature, which utilizes Google's Gemini model, automatically refines user-supplied prompts to potentially improve output quality.5 Consequently, the prompt formula detailed herein pertains to the *user-facing input* that is subsequently processed by this enhancement layer. The implication is that the user's prompt is not directly ingested by the core video synthesis mechanisms in its raw form. Instead, users are, in effect, prompting an intermediary system. Therefore, the effectiveness of a prompt is partly determined by how well it provides this intermediary layer with rich, interpretable information for augmentation.

The existence of "Flow," a distinct AI filmmaking tool designed to work with Veo3, Imagen (for images), and Gemini (for language processing) 1, also provides context. Flow offers features like a scene builder, asset management, and camera controls, suggesting that for more elaborate, multi-scene narratives or detailed post-generation editing, users might be guided towards this dedicated interface.1 The Veo3 API, particularly for the veo-3.0-generate-preview model, currently imposes an 8-second limit per generated video clip.7 This suggests that the direct API prompt formula is likely optimized for producing high-quality, concise clips, with Flow potentially orchestrating these into longer, more complex narratives. This report, however, focuses on deconstructing the prompt formula for direct interaction with the Veo3 model.

## **I. Veo3 Text-to-Video Prompt Formula**

The generation of video from text using Veo3 hinges on providing clear, descriptive, and well-structured prompts. The model, especially when coupled with its mandatory prompt enhancement feature, interprets natural language to translate a user's vision into a video sequence.

### **1\. Fundamental Components & Syntax**

The core of a Veo3 text-to-video prompt is built upon several semantic elements that, when combined, describe the desired output. The syntax is predominantly based on natural language, emphasizing descriptive richness over rigid symbolic codes. This approach aligns with the capabilities of the underlying LLM-based prompt rewriter (Gemini), which excels at processing and elaborating upon detailed textual input.4

* **Subject(s):** This component identifies the primary and secondary characters, objects, animals, or even abstract concepts that are central to the video.8 Subjects are defined using descriptive nouns and adjectives. The relative importance of subjects (primary vs. secondary) is typically conveyed through sentence structure, the level of detail provided, and narrative focus, rather than a specific symbolic syntax.  
  * *Example Snippet:* "An architectural rendering of a **white concrete apartment building** with flowing organic shapes, seamlessly blending with lush greenery and futuristic elements".8  
  * *Illustrative Example:* "A **majestic, snow-white owl** with piercing yellow eyes (primary subject) perches on a **gnarled, ancient oak branch** (secondary element)."  
* **Action(s):** Actions describe the activities, interactions, movements, behaviors, or states of being of the defined subject(s).8 These are articulated primarily through verbs and adverbs. Complex sequences of actions can be described narratively.  
  * *Example Snippet:* "A wise old owl **carefully circles** a clearing **looking around** to the forest floor. After a few moments, it **dives down** to a moonlit path and **sits** next to a badger".9  
  * *Illustrative Example:* "The owl **slowly turns its head**, its gaze **sweeping across** the moonlit forest floor, then **launches silently** into the air."  
* **Scene/Environment:** This element specifies the location, setting, time of day, and overall atmosphere of the video.8 It provides the backdrop and context for the subjects and their actions. Syntax involves prepositional phrases, descriptive adjectives, and contextual nouns.  
  * *Example Snippet:* "A low-angle shot shows an open, light purple door leading from a room with light purple walls and a gray floor to a **vibrant outdoor scene. Lush green grass and wildflowers spill from the doorway... Beyond the door, rolling green hills dotted with more wildflowers stretch towards a bright, clear sky**".10  
  * *Illustrative Example:* "**Deep within an ancient, mist-shrouded forest**, where **towering redwoods filter the faint moonlight**, creating an **atmosphere of quiet mystery**."  
* **Temporal Progression (within a single generation):** For the veo-3.0-generate-preview model, video generations are typically limited to 8 seconds.7 Directing changes or sequences of actions within this duration is achieved by structuring the prompt as a narrative. Sequential descriptive sentences or phrases guide the model to infer the flow of events. There is no evidence of explicit, time-coded commands (e.g., "at 2s, X happens"). The overall duration is set by the durationSeconds API parameter, but intra-clip pacing is derived from the prompt's narrative structure. For more complex temporal arrangements or longer narratives, the "Flow" tool is likely the intended solution for combining multiple generated clips.1  
  * *Example Snippet:* "A paper boat **sets sail** in a rain-filled gutter. It **navigates the current** with unexpected grace. It **voyages into a storm drain**, continuing its journey to unknown waters" (adapted from 9 example, structure from 9).  
  * *Illustrative Example:* "A single raindrop **forms** on a leaf. It **swells**, then **detaches**, **falling towards** a still puddle below. The impact **creates ripples** that spread outwards."

The underlying mechanism suggests that the more detailed and evocative the natural language input, the more effectively the prompt rewriter can interpret and augment the prompt for the core video generation engine.

### **2\. Visual Style & Control Modifiers: Syntax and Keywords**

Controlling the visual aesthetics of Veo3-generated videos involves using a combination of established cinematic and photographic terminology, as well as descriptive natural language, embedded within the overall prompt. There is no distinct "camera control language"; these modifiers are woven into the narrative. The specificity of these terms can significantly influence the final output, and the prompt rewriter may also contribute by adding or refining such details if the user's input is too basic.4

* **Aesthetic Descriptors:** These define the overall look and feel.  
  * Syntax: Keywords and phrases.  
  * Examples: "photorealistic," "cinematic," "anime style," "impressionistic," "film noir style" 8, "3D cartoon style render" 8, "pixelated art style" 10, "intricate origami art style" 9, "Ukiyo-e".9 Referencing specific artistic movements or artists is also effective.8  
* **Camera Specifications:**  
  * **Shot Type:** Keywords for framing the subject and scene.  
    * Syntax: "close-up," "extreme close-up" 8, "medium shot" 8, "long shot," "wide shot" 8, "establishing shot," "POV," "top-down shot".8  
  * **Camera Angle:** Keywords defining the camera's perspective relative to the subject.  
    * Syntax: "low angle" 8, "high angle," "eye-level" 8, "aerial view".8  
  * **Camera Movement:** Keywords describing the camera's dynamic motion.  
    * Syntax: "pan," "tilt," "zoom in/out" 9, "dolly" 2, "tracking shot" 9, "fast-tracking shot" 3, "static," "shaky handheld camera" 14, "camera booming up".13 The Flow tool also provides explicit camera controls.1  
  * **Lens Effects:** Keywords to simulate specific optical characteristics.  
    * Syntax: "bokeh," "wide-angle lens" 8, "telephoto lens," "depth of field" or "shallow depth of field" 8, "lens flare" 6, "fisheye lens" 11, "macro lens".11  
* **Lighting Descriptors:** Phrases describing light quality, direction, color, and mood.  
  * Syntax: "golden hour" 8, "studio lighting" 11, "dramatic shadows" 8, "neon glow," "warm lamplight" 10, "soft morning light," "dramatic chiaroscuro lighting" 15, "volumetric lighting".6  
* **Color Palette Control:** Descriptions of dominant colors, color relationships, or overall color mood.  
  * Syntax: "blue tones," "warm tones" 8, "monochromatic blue tones," "warm autumnal palette," "vibrant contrasting colors" 15, "pastel blue and pink tones," "cold muted tones".8  
* **Compositional Elements:** Keywords or descriptions influencing subject placement and visual arrangement.  
  * Syntax: "rule of thirds" 15, "centered subject," "dynamic low-angle shot".15 While some terms originate from photography or general image generation, their applicability to video composition is logical.

A consolidated reference for these terms is provided in Table 1\.

**Table 1: Keyword and Syntax Compendium for Visual Styles & Camera Control**

| Category | Keywords/Phrases | Definition/Purpose | Illustrative Prompt Snippet Example |
| :---- | :---- | :---- | :---- |
| **Aesthetics** | "photorealistic", "cinematic", "anime style", "impressionistic", "film noir", "3D cartoon render", "pixelated art" | Defines the overall artistic look and feel of the video. | "...**in an intricate origami art style** using complex, angular folds..." 9 |
| **Shot Type** | "close-up", "extreme close-up", "medium shot", "long shot", "wide shot", "establishing shot", "POV", "top-down" | Specifies the framing of the subject and how much of the scene is visible. | "**A medium shot** frames an old sailor..." 9 |
| **Camera Angle** | "low angle", "high angle", "eye-level", "aerial view", "Dutch angle" | Determines the camera's vertical position and tilt relative to the subject. | "**A low-angle shot** shows an open, light purple door..." 10 |
| **Camera Movement** | "pan", "tilt", "zoom in/out", "dolly", "tracking shot", "fast-tracking shot", "static", "shaky handheld" | Describes the physical movement of the camera during the shot. | "**A fast-tracking shot** through a futuristic city..." 3 |
| **Lens Effects** | "bokeh", "wide-angle", "telephoto", "depth of field", "shallow depth of field", "lens flare", "fisheye", "macro" | Simulates optical effects associated with different camera lenses and settings. | "...**extreme close-up with a shallow depth of field** of a puddle..." 6 |
| **Lighting** | "golden hour", "studio lighting", "dramatic shadows", "neon glow", "warm lamplight", "volumetric lighting" | Defines the quality, direction, color, and mood of the light in the scene. | "...bathed in **warm, soft, late-afternoon sunlight** filtering into a quintessential 1960s kitchen." 10 |
| **Composition** | "rule of thirds", "centered subject", "leading lines", "symmetry", "asymmetrical balance" | Influences the arrangement of elements within the frame for visual impact. | "...**rule of thirds composition**, dynamic low-angle shot." 15 (Image context, applicable to video) |
| **Color Palette** | "monochromatic blue tones", "warm autumnal palette", "vibrant contrasting colors", "pastel tones", "muted colors" | Specifies dominant colors, color relationships, or overall color mood of the video. | "Cinematic close-up shot of a sad woman riding a bus in the rain, **cool blue tones**, sad mood." 8 |

### **3\. Advanced Audio & Speech Generation Formula**

A distinguishing feature of Veo3 is its native audio generation capability, allowing for the inclusion of synchronized dialogue, sound effects (SFX), music, and ambient sounds directly from the prompt.1 The API parameter generateAudio: true is essential for enabling this with the veo-3.0-generate-preview model.6 The system appears to parse narrative context for audio cues, facilitated by the prompt rewriter, which can add transcriptions and sound effects if not explicitly detailed by the user.4

* **Dialogue/Speech:**  
  * **Syntax for Assigning Dialogue:**  
    * Direct attribution: Subject Name: "dialogue\_text" (e.g., Cartographer: "According to this old sea chart..." 9). This method allows for clear assignment, and character names can be given.19  
    * Narrative attribution: Dialogue can be woven into the descriptive text, with attribution clarified through action (e.g., The man turns to the woman and asks, "What are we doing here?" The woman then looks at the man... and responds, "Do you ever listen to what I say?" 19).  
    * Implied attribution: Context can imply the speaker (e.g., A detective interrogates a nervous-looking rubber duck. "Where were you on the night of the bubble bath?\!" he quacks. 3).  
  * **Syntax for Voice Characteristics (Tone, Emotion, Accent):** These are typically conveyed using descriptive adverbs and adjectives within the narrative or attribution.  
    * Examples: "the woman speaks **angrily**" 19, "the badger **stammered**" 9, "the owl hooted **thoughtfully**" 9, "Detective's **stern** quack, **nervous** squeaks from rubber duck".3 Users have also attempted to specify accents, such as "speaks... in Brazilian Portuguese".20 The more descriptive the language, the better the model can interpret nuanced vocal delivery.  
* **Sound Effects (SFX):**  
  * **Syntax for Requesting Diegetic Sounds (related to on-screen action):**  
    * Explicit tagging: Audio: \[sound\_description\_1, sound\_description\_2,...\] (e.g., Audio: wings flapping, birdsong, loud and pleasant wind rustling 9).  
    * Narrative description: SFX can be described naturally within the prompt (e.g., "The scene is accompanied by the distant wail of a police siren" 21; "making noise of rustling dried autumn leaves" 9).  
  * **Syntax for Requesting Non-Diegetic Sounds (mood enhancers, transitions):** While less explicitly documented for SFX distinct from music, these would likely be included in the Audio: tag or as part of the general mood description (e.g., Audio: tense stinger, whoosh transition).  
* **Music/Score:**  
  * **Syntax for Defining Genre, Mood, Instrumentation, or Tempo:** These are often specified within an Audio: tag or as a general descriptive sentence.  
    * Examples: Audio: A light orchestral score with woodwinds throughout with a cheerful, optimistic rhythm, full of innocent curiosity. 9; "The scene is accompanied by soft music in the background".8  
* **Ambient Sound:**  
  * **Syntax for Background Environmental Audio:** Can be part of the Audio: tag or described naturally within the scene setting.  
    * Examples: Audio: birds chirping, wind rustling leaves 21; "traffic noises in the background of a city street scene, birds singing in a park" 2; "roaring waves, whistling wind".3  
* **Synchronization Cues:** The timing of audio events relative to visual actions is primarily implied by the narrative structure of the prompt. Audio elements are described in conjunction with the visual actions they should accompany to achieve synchronization.1  
  * Example: "diced onions hitting a scorching hot pan, instantly creating a dramatic sizzle. Audio: distinct sizzle.".9 The prompt directly links the visual action (onions hitting pan) with the auditory result (sizzle).

It is important to note that some user experiences suggest that audio generation capabilities might be most robust in the primary "Text-to-Video" mode, with potential inconsistencies in other modes or interfaces like SceneBuilder.21 This highlights a practical consideration: the full audio formula might not be universally applicable across all Veo features with the same level of reliability.

Table 2 provides a summary of keywords and syntax for audio elements.

**Table 2: Keyword and Syntax Compendium for Audio Elements**

| Category | Syntax/Patterns | Keywords/Descriptors | Illustrative Prompt Snippet Example |
| :---- | :---- | :---- | :---- |
| **Dialogue Attribution** | Character Name: "Dialogue text.", Narrative description (He said, "..."), Implied by context. | Character names, action verbs indicating speech (e.g., says, asks, responds, quacks). | Cartographer: "According to this old sea chart, the lost island isn't myth\!" 9 |
| **Voice Characteristics** | Descriptive adverbs/adjectives modifying speech (e.g., "... he quacks sternly.", "She whispered softly..."). | "angrily", "softly", "sternly", "nervous", "thoughtfully", specific accents (e.g. "British accent"). | "...the badger stammered, trying to comprehend it." 9 |
| **Sound Effects (SFX)** | Audio:., Narrative description (e.g., "The sound of thunder rumbled."). | Specific sounds (e.g., "footsteps", "door creak", "explosion", "sizzle", "car horn"). | Audio: wings flapping, birdsong, loud and pleasant wind rustling... 9 |
| **Music/Score** | Audio: \[Music description\]., Narrative description (e.g., "A sad piano melody plays."). | Genre (e.g., "orchestral", "electronic", "jazz"), mood (e.g., "cheerful", "tense", "epic"), instruments (e.g., "woodwinds", "piano", "drums"), tempo (e.g., "fast rhythm"). | Audio: A light orchestral score with woodwinds throughout with a cheerful, optimistic rhythm, full of innocent curiosity. 9 |
| **Ambient Sound** | Audio: \[Ambient sounds\]., Narrative description of environmental sounds. | "city bustle", "forest sounds", "ocean waves", "wind rustling leaves", "birds chirping", "traffic noise". | "Audio: birds chirping, wind rustling leaves," 21 or "traffic noises in the background of a city street scene".2 |
| **Synchronization** | Sequential description of action and corresponding sound; placing audio description immediately after or within the related visual action description. | (Implicit through structure) | "diced onions hitting a scorching hot pan, instantly creating a dramatic sizzle. Audio: distinct sizzle." 9 |

### **4\. Structuring, Sequencing, and Control Parameters**

The overall structure of a Veo3 prompt and the explicit API parameters governing the generation process are integral to the prompt formula. Effective prompting requires crafting a coherent narrative within the technical boundaries defined by these parameters, especially for the veo-3.0-generate-preview model.

* **Order of Elements:** While there isn't a strictly enforced order, prompts generally achieve better clarity by following a natural language narrative flow. A common and effective sequence is: Scene/Setting → Subject(s) → Action(s) → Visual Style/Camera Modifiers. Audio descriptions can be interspersed where relevant or follow the associated visual descriptions. Google's own guidance suggests an order of: Subject, Context, Action, Style, Camera motion, Composition, Ambiance, and finally, Audio.8 This serves as a robust general guideline.  
* **Weighting/Emphasis:** Direct syntactic weighting of prompt terms (e.g., (word:1.5) or word++) is not documented for Veo3 in the available materials. Emphasis on particular elements is therefore likely achieved implicitly through:  
  * **Detail and Specificity:** Providing more descriptive and precise language for key elements.  
  * **Order:** Placing the most critical elements earlier in the prompt.  
  * **Clarity:** Using unambiguous language for important aspects.  
  * **Repetition (Judicious Use):** Carefully repeating a core concept or keyword, though overuse can lead to undesirable outcomes. The mandatory prompt rewriter 4 may also interpret intended emphasis from these natural language cues and the overall structure of the prompt.  
* **Negative Prompts:** Veo3 supports negative prompting through the negativePrompt: "text" API parameter.5 This allows users to specify elements they wish to discourage the model from generating.  
  * **Syntax:** The value is a text string containing keywords or descriptions of unwanted elements.  
  * **Content Guidance:** It is more effective to describe *what you don't want to see* (e.g., negativePrompt: "blurry, text, watermark, people") rather than using instructive or negating language like "no blurriness" or "don't show people".8 For instance, to exclude walls and frames, one might use negativePrompt: "wall, frame".8  
* **Multi-Sentence/Paragraph Prompts:** Veo3, particularly with its prompt enhancement layer, appears capable of handling longer, multi-sentence prompts for a single 8-second clip.9 These are typically interpreted as a continuous narrative. If the original prompt is fewer than 30 words, the rewritten prompt used by the model is delivered in the API response, offering some insight into the enhancement process.4  
* **Parameters/Keywords for Direct Control (API Level):** The behavior of Veo3 is significantly governed by parameters set at the API call level. For the veo-3.0-generate-preview model, these parameters and their specific constraints are critical. (Refer to Table 3 in Section III for a comprehensive list). Key parameters include prompt, durationSeconds (fixed at 8s for veo-3.0-generate-preview), aspectRatio ("16:9" only for veo-3.0-generate-preview), negativePrompt, the mandatory enhancePrompt: true, generateAudio: true (for audio), seed (for reproducibility), sampleCount (1-2 for veo-3.0-generate-preview), and personGeneration (safety setting).4

The interplay between a well-crafted descriptive prompt and correctly configured API parameters is essential for achieving desired results. The API parameters establish the operational boundaries, while the text prompt guides the creative content within those boundaries.

## **II. Veo3 Image-to-Video Prompt Formula**

Veo3 also supports generating videos from a static input image, a capability referred to as image-to-video generation.4 In this mode, the provided image serves as the starting point, and the text prompt directs its animation, transformation, and the introduction of audio.

### **1\. Core Structure with Image Input**

When an input image is provided via the image API parameter (which accepts either a Base64-encoded image string or a Cloud Storage URI 5), the role of the text prompt (prompt parameter) shifts. Instead of generating the entire scene from scratch, the text prompt instructs Veo3 on how to animate, evolve, or stylize the content of the supplied image. For veo-3.0-generate-preview, the maximum input image size is 20 MB.7 Images with aspect ratios differing from the recommended 16:9 (for this model) or 9:16 (for other models) may be center-cropped or resized.6

The essential components of the text prompt in an image-to-video context typically include:

* A description of the desired **motion** or **animation** to be applied to the subjects or elements within the image.  
* Instructions for how the scene or subjects in the image should **evolve or transform** over the video's duration.  
* Optionally, descriptions of new visual **styles** to apply, or new elements to introduce.  
* Audio descriptions (dialogue, SFX, music) if sound is desired, similar to text-to-video prompts.

The text prompt, while technically optional if an image is provided 6, is practically necessary to guide any animation or change. The image provides the initial "subject" and "scene," and the text prompt provides the "action" and "narrative."

Current documentation and user feedback suggest that image-to-video functionality, particularly for achieving complex, controlled animations or maintaining strict character/scene consistency from the input image, may be less mature or more prone to unexpected alterations compared to text-to-video generation.22 Users have reported issues such as the model altering the original image in unintended ways or difficulties in preserving character consistency.22 This indicates that the "formula" for image-to-video might be more emergent and require greater user experimentation.

### **2\. Prompting for Animation & Transformation**

Guiding the animation and transformation of an input image relies on descriptive language in the text prompt.

* **Describing Motion/Animation:** Use action verbs and adverbs to describe how elements within the image should move. The animation description appears to be holistic rather than based on explicit commands for specific image regions.  
  * *Illustrative Example (Image: Static bird on a branch):* prompt: "The bird in the image ruffles its feathers, then takes flight, soaring upwards and out of frame to the right."  
* **Describing Evolution/Transformation:** The text prompt should describe the target state of the transformation or the process of change itself.  
  * *Illustrative Example (Image: A caterpillar on a leaf):* prompt: "The caterpillar in the image slowly spins a chrysalis around itself. The chrysalis then cracks open, and a vibrant butterfly emerges, unfurling its wings." (Inspired by transformation concepts like "A block of marble turns into a griffon sculpture" 9).  
* **Applying a New Style:** Specify the desired new aesthetic in the text prompt, using similar keywords as in text-to-video style prompting.  
  * *Illustrative Example (Image: A modern cityscape photograph):* prompt: "Transform the cityscape in the image into a 'Blade Runner' style scene, with rain, neon lights, and flying vehicles animating through the sky." Veo3 documentation mentions "Accurate Style Control: Control the artistic style based on reference images" 12, and "reference powered video" for consistency and style matching, particularly with Veo 2 in the Flow interface.2 However, the precise API-level syntax for using a *style reference image* in conjunction with a *content input image* for the veo-3.0-generate-preview model is not extensively detailed for a single API call. More commonly, the text prompt would describe the desired style change to be applied to the primary input image.

The "formula" for animating specific parts of an image or orchestrating complex transformations via the direct veo-3.0-generate-preview API appears to be under-documented. It likely relies on the model's general semantic understanding of the text prompt in relation to the visual content of the image, rather than specific sub-region animation syntax or explicit pathing commands. Advanced features like "Object add and remove" and "reference powered video" 2 are mentioned in the context of Veo (sometimes Veo 2\) and the Flow tool, suggesting these more granular controls might be primarily accessed or better managed through Flow. Achieving consistent character animation from a single static image using only the basic API call can be challenging.22

### **3\. Audio in Image-to-Video**

Incorporating audio when generating a video from an image follows similar principles to text-to-video. The generateAudio: true API parameter must be set.6

* **Prompting for Dialogue, SFX, and Music:** Audio elements are described in the text prompt in relation to the (now animated) image content. The syntax and descriptive methods outlined in Section I.3 (Advanced Audio & Speech Generation Formula) apply.  
  * *Illustrative Example (Image: A static dog):* prompt: "The dog in the image wags its tail excitedly and barks. Audio: happy barks, sound of a squeaky toy, upbeat playful music."  
* **Synchronization:** Audio should ideally synchronize with the animated elements as described in the text prompt and interpreted by the model.

However, user reports indicate that audio generation in image-to-video mode can sometimes be unreliable, with instances of missing audio even when requested.22 This suggests a potential disparity between the intended audio prompting formula and the current robustness of its implementation for image-to-video outputs. While the methods for *specifying* audio are consistent, its reliable generation and synchronization with animated image content may require more iteration or be subject to ongoing improvements.

### **4\. Controlling Image Interpretation**

Guiding Veo3 on which parts of an input image to focus on for animation, or how to interpret ambiguous content, primarily relies on the specificity and clarity of the descriptive language used in the text prompt.

* **Emphasis through Description:** Users should craft text that clearly identifies and emphasizes the elements of the image that are intended to be the central subject of animation or transformation.  
  * *Illustrative Example (Image: A room with multiple objects, including a prominent grandfather clock):* prompt: "Focusing on the tall grandfather clock in the image, its pendulum begins to swing rhythmically and its hands slowly advance. The surrounding furniture remains still. Audio: steady ticking of the clock, a soft chime." (Achieving such precise differential animation from a single prompt and image is an advanced use case, and its consistent success is not fully confirmed by available documentation for the basic API).

Currently, there are no documented API parameters or explicit syntax (such as attention masking or region-of-interest selection tools) for the veo-3.0-generate-preview model that allow users to directly designate specific areas of the input image for animation. The success of guiding the model's interpretation hinges on its ability—and that of the prompt rewriter—to accurately correlate detailed textual descriptions with the visual elements present in the provided image. Comparisons to other models, like Tencent's Hunyuan which reportedly allows circling regions or drawing arrows for targeted prompting 23, highlight features not currently evident in Veo3's public API documentation.

## **III. Key API Parameters and Global Controls (veo-3.0-generate-preview)**

The Veo3 prompt formula is not solely defined by the textual content but is also critically shaped by the API parameters that govern the generation request. For the veo-3.0-generate-preview model, these parameters impose specific constraints and enable core functionalities. Understanding and correctly utilizing these parameters is essential for effective prompting.

Table 3 provides a consolidated reference for the key API parameters relevant to the veo-3.0-generate-preview model.

**Table 3: Veo3 API Parameters for veo-3.0-generate-preview**

| Parameter Name | Data Type | Required (T2V) | Required (I2V) | Description & Purpose | Accepted Values for veo-3.0-generate-preview | Default Value |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| modelId | string | Yes | Yes | Specifies the video generation model to use. | veo-3.0-generate-preview | N/A (Must be specified) |
| prompt | string | Yes | Optional | The primary text description guiding video generation. For I2V, guides animation/transformation of the input image. | Natural language text. English only.7 | N/A if image provided, else required. |
| image | object / string | No | Yes | Input image for image-to-video generation. Can be Base64 encoded bytes or a Cloud Storage URI. | {bytesBase64Encoded: string, mimeType: "image/jpeg" or "image/png"} or gs://BUCKET\_NAME/path/to/image.png. Max 20MB.7 Recommended 1280x720 or 720x1280.6 | N/A |
| durationSeconds | integer | Yes | Yes | Desired length of the generated video file(s). | Fixed at 8 seconds.6 | 8 |
| negativePrompt | string | Optional | Optional | Text string describing elements to discourage the model from generating. | Natural language text. | Empty string |
| enhancePrompt | boolean | Yes | Yes | Uses Gemini (LLM) to enhance the user's prompt. Cannot be disabled for this model. | true (Mandatory).4 | true |
| seed | uint32 | Optional | Optional | A number to make generated videos deterministic. Same seed \+ same parameters \= same video. | 0 \- 4294967295\.5 | Random |
| storageUri / OUTPUT\_STORAGE\_URI | string | Optional | Optional | Cloud Storage bucket URI to store output videos. If not provided, video bytes are returned in the response. | e.g., gs://BUCKET\_NAME/SUBDIRECTORY/.5 | Response returns base64 video bytes. |
| sampleCount / RESPONSE\_COUNT | integer | Yes | Yes | The number of output videos requested. | 1 \- 2\.7 (General Veo API might support up to 4, but veo-3.0-generate-preview is limited). | 1 |
| aspectRatio | string | Yes | Yes | Defines the aspect ratio of the generated videos. | "16:9" (landscape) only. "9:16" (portrait) is NOT supported by this model.6 | "16:9" |
| personGeneration / PERSON\_SAFETY\_SETTING | string | Optional | Optional | Safety setting controlling whether people or face generation is allowed. | "allow\_adult" (allows adults only), "disallow" (disallows any people/faces).5 | "allow\_adult" |
| generateAudio | boolean | Yes | Yes | Generates audio (dialogue, SFX, music) for the video. | true (Required for audio generation with this model).6 | false (Must be set to true for audio) |

**Global Settings and Operational Constraints:**

Beyond these direct parameters, several global factors influence Veo3 usage:

* **Safety Filters:** Vertex AI applies safety filters across Veo to help ensure that generated videos and uploaded images do not contain offensive or harmful content. Prompts that violate responsible AI guidelines will be blocked.8 The personGeneration parameter offers a specific control within this framework.  
* **API Limits:** For veo-3.0-generate-preview, there is a maximum of 10 API requests per minute per project.4  
* **Supported Resolution and Framerate:** The veo-3.0-generate-preview model generates videos at 720p resolution and 24 FPS.4  
* **Prompt Language:** Only English language prompts are currently supported for veo-3.0-generate-preview.7 Users attempting prompts in other languages, even for dialogue, may encounter errors.20

The parameters for veo-3.0-generate-preview are notably more restrictive in some areas (e.g., fixed 8-second duration, single 16:9 aspect ratio, mandatory prompt enhancement, maximum of 2 samples per request) compared to capabilities described for other Veo models (like Veo 2\) or potentially available through the more flexible "Flow" interface. This underscores that the prompt formula for this specific API endpoint must operate strictly within these defined technical boundaries.

## **IV. Conclusion: Synthesizing the Veo3 Prompt Grammar**

The prompt formula for Google's Veo3, particularly the veo-3.0-generate-preview model, is a multifaceted system that relies on a dynamic interplay between user-provided natural language, a sophisticated AI-driven prompt enhancement layer (Gemini), and a set of explicit API controls. It is not a static, purely syntactic set of rules but rather a method of communication that leverages the model's advanced understanding of descriptive language.

**Core Principles of Veo3 Prompting (veo-3.0-generate-preview):**

1. **Descriptive Natural Language is Key:** The foundation of a successful Veo3 prompt is rich, detailed, and clear natural language. The model excels when provided with evocative descriptions of subjects, actions, scenes, visual styles, and audio elements.  
2. **Mandatory Prompt Enhancement:** The non-disablable enhancePrompt feature means that user prompts are always processed and potentially augmented by an intermediary LLM. Effective prompting involves crafting input that this enhancement layer can optimally interpret and build upon.  
3. **Integrated Cinematic and Audio Terminology:** Visual controls (camera shots, angles, movements, lighting) and audio elements (dialogue, SFX, music) are specified by embedding relevant terminology directly within the narrative structure of the prompt.  
4. **Adherence to API Constraints:** The veo-3.0-generate-preview model operates under specific API parameter limitations (e.g., 8-second duration, 16:9 aspect ratio, English-only prompts, specific safety settings). The textual prompt must be designed to work effectively within these technical boundaries.  
5. **Evolving Capabilities and Limitations:** While Veo3 demonstrates powerful text-to-video capabilities, especially with synchronized audio, areas such as fine-grained control in image-to-video animation, consistent character rendering from images, and reliable audio in all operational modes appear to be subjects of ongoing development or may require the use of auxiliary tools like Google Flow.

**Recommendations for Effective Veo3 Prompt Construction:**

* **Prioritize Specificity and Detail:** Clearly define all aspects of the desired video. Vague prompts are more likely to yield generic or unintended results. Use precise adjectives, adverbs, and nouns.  
* **Employ Narrative Structure:** For sequential actions, temporal progression within the 8-second limit, or synchronized audio-visual events, structure the prompt as a coherent narrative. Describe events in the order they should occur.  
* **Utilize Established Vocabulary:** Incorporate recognized terminology from filmmaking, photography, and audio production to guide visual style, camera work, lighting, composition, and sound design.  
* **Leverage Negative Prompts Strategically:** Use the negativePrompt API parameter to explicitly exclude unwanted elements, describing what *not* to include rather than using negating instructions.  
* **Understand and Respect API Parameters:** Be fully aware of the fixed constraints and available options for the veo-3.0-generate-preview model (duration, aspect ratio, audio enablement, etc.) and configure them appropriately.  
* **Consider Complementary Tools for Complex Projects:** For narratives exceeding the 8-second clip limit, requiring intricate multi-scene editing, or demanding advanced character consistency across multiple shots, explore Google's "Flow" tool, as it is designed for such higher-level filmmaking tasks.  
* **Iterate and Experiment:** Generative AI for video is a rapidly evolving field. The optimal prompting strategies may continue to refine. Users should engage in iterative prompting, observe the results, and adjust their approach to develop a deeper understanding of the model's responses.

In essence, mastering the Veo3 prompt formula involves developing a nuanced understanding of how to "converse" with its AI systems—providing sufficiently rich and structured information that can be effectively interpreted and translated into compelling video and audio by its enhancement and generation engines, all while operating within the defined technical framework.

#### **Works cited**

1. VEO 3 FLOW Full Tutorial \- How To Use VEO3 in FLOW Guide \- Hugging Face, accessed on June 2, 2025, [https://huggingface.co/blog/MonsterMMORPG/veo-3-flow-full-tutorial-how-to-use-veo3-in-flow](https://huggingface.co/blog/MonsterMMORPG/veo-3-flow-full-tutorial-how-to-use-veo3-in-flow)  
2. Fuel your creativity with new generative media models and tools, accessed on June 2, 2025, [https://blog.google/technology/ai/generative-media-models-io-2025/](https://blog.google/technology/ai/generative-media-models-io-2025/)  
3. What Is Google Veo 3? Inside the Viral AI Video Model with Real ..., accessed on June 2, 2025, [https://getimg.ai/blog/what-is-google-veo-3-inside-the-viral-ai-video-model-with-real-sound](https://getimg.ai/blog/what-is-google-veo-3-inside-the-viral-ai-video-model-with-real-sound)  
4. How to Use Google Veo 3 Now (3 Methods) \- Apidog, accessed on June 2, 2025, [https://apidog.com/blog/google-veo-3-api/](https://apidog.com/blog/google-veo-3-api/)  
5. Veo | AI Video Generator | Generative AI on Vertex AI | Google Cloud, accessed on June 2, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/video/generate-videos](https://cloud.google.com/vertex-ai/generative-ai/docs/video/generate-videos)  
6. Veo on Vertex AI API | Generative AI on Vertex AI | Google Cloud, accessed on June 2, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/veo-video-generation](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/veo-video-generation)  
7. Veo 3 Generate 001 Preview allowlist | Generative AI on Vertex AI ..., accessed on June 2, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/models/veo/3-0-generate-preview](https://cloud.google.com/vertex-ai/generative-ai/docs/models/veo/3-0-generate-preview)  
8. Vertex AI video generation prompt guide \- Google Cloud, accessed on June 2, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/video/video-gen-prompt-guide](https://cloud.google.com/vertex-ai/generative-ai/docs/video/video-gen-prompt-guide)  
9. Veo \- Google DeepMind, accessed on June 2, 2025, [https://deepmind.google/models/veo/](https://deepmind.google/models/veo/)  
10. Announcing Veo 3, Imagen 4, and Lyria 2 on Vertex AI | Google Cloud Blog, accessed on June 2, 2025, [https://cloud.google.com/blog/products/ai-machine-learning/announcing-veo-3-imagen-4-and-lyria-2-on-vertex-ai](https://cloud.google.com/blog/products/ai-machine-learning/announcing-veo-3-imagen-4-and-lyria-2-on-vertex-ai)  
11. Prompt and image attribute guide | Generative AI on Vertex AI \- Google Cloud, accessed on June 2, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/image/img-gen-prompt-guide](https://cloud.google.com/vertex-ai/generative-ai/docs/image/img-gen-prompt-guide)  
12. Veo 3 AI Video Generator \- Pollo AI, accessed on June 2, 2025, [https://pollo.ai/m/veo/veo-3](https://pollo.ai/m/veo/veo-3)  
13. Veo 3 Beginner Tutorial How to move the camera, consistent characters, cut to \- YouTube, accessed on June 2, 2025, [https://www.youtube.com/watch?v=\_GNoUXKo4uo](https://www.youtube.com/watch?v=_GNoUXKo4uo)  
14. Exploring Google Veo 3's AI Prompt Theory \- TikTok, accessed on June 2, 2025, [https://www.tiktok.com/@cypruseats/video/7508410321925573910](https://www.tiktok.com/@cypruseats/video/7508410321925573910)  
15. Google Gemini AI \- Reddit, accessed on June 2, 2025, [https://www.reddit.com/r/GoogleGeminiAI/controversial/?after=dDNfMWpxd2M4cQ%3D%3D\&sort=controversial\&t=day\&feedViewType=cardView](https://www.reddit.com/r/GoogleGeminiAI/controversial/?after=dDNfMWpxd2M4cQ%3D%3D&sort=controversial&t=day&feedViewType=cardView)  
16. Photography Basics for Beginners | CameraPro Blogs, accessed on June 2, 2025, [https://www.camerapro.com.au/news-and-reviews/post/photography-basics-for-beginners](https://www.camerapro.com.au/news-and-reviews/post/photography-basics-for-beginners)  
17. google's veo 3 generates AI videos from text with dialogues, voice-overs and sound effects, accessed on June 2, 2025, [https://www.designboom.com/technology/google-veo-3-generates-ai-videos-text-dialogues-voice-overs-sound-effects-05-22-2025/](https://www.designboom.com/technology/google-veo-3-generates-ai-videos-text-dialogues-voice-overs-sound-effects-05-22-2025/)  
18. Google Sharpens its Visual and Music AI Edge: Veo 3, Imagen 4, and Lyria 2 Arrive on Vertex AI \- Aragon Research, accessed on June 2, 2025, [https://aragonresearch.com/google-sharpens-its-visual-and-music-ai/](https://aragonresearch.com/google-sharpens-its-visual-and-music-ai/)  
19. Google Veo 3 \- Basic Prompt Tutorial \- YouTube, accessed on June 2, 2025, [https://www.youtube.com/watch?v=p-svK8d3iLc\&pp=0gcJCdgAo7VqN5tD](https://www.youtube.com/watch?v=p-svK8d3iLc&pp=0gcJCdgAo7VqN5tD)  
20. VEO 3 keeps saying "prompts only in english", even when the prompt is already in english \- Gemini Apps Community \- Google Help, accessed on June 2, 2025, [https://support.google.com/gemini/thread/347700144/veo-3-keeps-saying-prompts-only-in-english-even-when-the-prompt-is-already-in-english?hl=en](https://support.google.com/gemini/thread/347700144/veo-3-keeps-saying-prompts-only-in-english-even-when-the-prompt-is-already-in-english?hl=en)  
21. For some reason my veo3 videos stopped having audio/people talking \- Gemini Apps Community \- Google Help, accessed on June 2, 2025, [https://support.google.com/gemini/thread/346248453/for-some-reason-my-veo3-videos-stopped-having-audio-people-talking?hl=en](https://support.google.com/gemini/thread/346248453/for-some-reason-my-veo3-videos-stopped-having-audio-people-talking?hl=en)  
22. Is it possible to transform images into videos via a prompt using Veo ..., accessed on June 2, 2025, [https://www.reddit.com/r/Bard/comments/1kypyoj/is\_it\_possible\_to\_transform\_images\_into\_videos/](https://www.reddit.com/r/Bard/comments/1kypyoj/is_it_possible_to_transform_images_into_videos/)  
23. Veo 3 and Imagen 4, and a new tool for filmmaking called Flow | Hacker News, accessed on June 2, 2025, [https://news.ycombinator.com/item?id=44044043](https://news.ycombinator.com/item?id=44044043)